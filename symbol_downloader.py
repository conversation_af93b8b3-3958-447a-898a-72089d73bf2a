import os
import requests
import yaml
from datetime import datetime

def get_config():
    """Loads the configuration from config.yaml."""
    with open(os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.yaml"), 'r') as f:
        return yaml.safe_load(f)

def download_file(url, destination):
    """Downloads a file from a URL to a destination."""
    response = requests.get(url)
    response.raise_for_status()
    with open(destination, 'wb') as f:
        f.write(response.content)

def main():
    """Main function to download and backup the symbol files."""
    config = get_config()
    mastersymbol_dir = 'mastersymbol'
    remote_urls = config['general']['fyers_api_url']
    
    # Ensure remote_urls is a list
    if isinstance(remote_urls, str):
        remote_urls = [remote_urls]

    # Create or clear the mastersymbol directory
    if not os.path.exists(mastersymbol_dir):
        os.makedirs(mastersymbol_dir)
    else:
        for f in os.listdir(mastersymbol_dir):
            os.remove(os.path.join(mastersymbol_dir, f))

    # Process each URL
    for url in remote_urls:
        # Determine file type from URL
        if 'NSE_CM.csv' in url:
            local_file_path = 'NSE_CM.csv'
            file_prefix = 'NSE_CM'
        elif 'NSE_FO.csv' in url:
            local_file_path = 'NSE_FO.csv'
            file_prefix = 'NSE_FO'
        else:
            # Default fallback
            local_file_path = 'NSE_FO.csv'
            file_prefix = 'NSE_FO'
            
        # Backup the existing file
        if os.path.exists(local_file_path):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = os.path.join(mastersymbol_dir, f"{file_prefix}_{timestamp}.csv")
            os.rename(local_file_path, backup_path)
            print(f"Backed up existing file to {backup_path}")

        # Download the new file
        print(f"Downloading latest symbol file from {url}...")
        download_file(url, local_file_path)
        print(f"Downloaded and saved new symbol file to {local_file_path}")

if __name__ == '__main__':
    main()