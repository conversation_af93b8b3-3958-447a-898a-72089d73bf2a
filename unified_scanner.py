"""
Unified scanner that handles both NSE_CM.csv (equity) and NSE_FO.csv (options) flows.
Processes NSE_CM.csv first, then NSE_FO.csv, and generates separate reports for each.
"""

import logging
import sys
import os
import subprocess
from datetime import datetime
from typing import Dict, Any, List

# Import our modules
from fyers_config import setup_logging
from config_loader import get_config
from equity_scanner import EquityScanner
from index_scanner import IndexScanner
from equity_report_generator import EquityReportGenerator
from report_generator import ReportGenerator

logger = logging.getLogger(__name__)

class UnifiedScanner:
    """Unified scanner that orchestrates both equity and options scanning."""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialize the unified scanner.
        
        Args:
            config_path: Path to the configuration file
        """
        self.config = get_config(config_path)
        self.results = {
            'equity': {
                'symbols': [],
                'summary': {},
                'reports': {}
            },
            'options': {
                'symbols': [],
                'summary': {},
                'reports': {}
            }
        }
        
    def validate_prerequisites(self) -> bool:
        """
        Validate that all required files and dependencies are available.
        
        Returns:
            True if all prerequisites are met, False otherwise
        """
        logger.info("Validating prerequisites...")
        
        # Validate configuration
        if not self.config.validate_config():
            logger.error("Configuration validation failed. Please check config.yaml")
            return False
        
        # Check if required CSV files exist (they will be downloaded if not)
        required_files = ["NSE_CM.csv", "NSE_FO.csv"]
        missing_files = []
        
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            logger.info(f"Missing CSV files will be downloaded: {', '.join(missing_files)}")
        
        try:
            # Check if required packages are available
            import yaml
            import fyers_apiv3
            logger.info("All prerequisites satisfied")
            return True
        except ImportError as e:
            logger.error(f"Missing required package: {e}")
            logger.error("Please install required packages using: pip install pyyaml fyers-apiv3")
            return False
    
    def download_symbol_files(self) -> bool:
        """
        Download the latest symbol files from configured URLs.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("Downloading latest symbol files...")
            subprocess.run(["python", "symbol_downloader.py"], check=True)
            logger.info("Symbol files downloaded successfully")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to download symbol files: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during symbol file download: {e}")
            return False
    
    def scan_equity_symbols(self) -> bool:
        """
        Scan equity symbols from NSE_CM.csv.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("=" * 60)
            logger.info("STARTING EQUITY SCANNING (NSE_CM.csv)")
            logger.info("=" * 60)
            
            # Check if NSE_CM.csv exists
            if not os.path.exists("NSE_CM.csv"):
                logger.warning("NSE_CM.csv not found. Skipping equity scanning.")
                return True
            
            # Initialize equity scanner
            equity_scanner = EquityScanner(self.config)
            
            # Run equity scan
            filtered_equity_symbols = equity_scanner.scan_symbols()
            
            if not filtered_equity_symbols:
                logger.warning("No equity symbols found matching the criteria")
                self.results['equity']['symbols'] = []
                self.results['equity']['summary'] = {'total_symbols': 0}
                return True
            
            # Generate summary statistics
            summary_stats = equity_scanner.get_scan_summary(filtered_equity_symbols)
            
            # Generate reports
            equity_report_generator = EquityReportGenerator(
                output_dir=self.config.output_dir, 
                config=self.config
            )
            
            report_files = equity_report_generator.generate_full_report(
                filtered_equity_symbols, 
                summary_stats
            )
            
            # Store results
            self.results['equity']['symbols'] = filtered_equity_symbols
            self.results['equity']['summary'] = summary_stats
            self.results['equity']['reports'] = report_files
            
            logger.info("=" * 60)
            logger.info("EQUITY SCANNING COMPLETED")
            logger.info("=" * 60)
            logger.info(f"Equity symbols found: {len(filtered_equity_symbols)}")
            logger.info(f"Equity CSV Report: {report_files['csv_report']}")
            logger.info(f"Equity Summary Report: {report_files['summary_report']}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error during equity scanning: {e}", exc_info=True)
            return False
    
    def scan_options_symbols(self) -> bool:
        """
        Scan options symbols from NSE_FO.csv.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("=" * 60)
            logger.info("STARTING OPTIONS SCANNING (NSE_FO.csv)")
            logger.info("=" * 60)
            
            # Check if NSE_FO.csv exists
            if not os.path.exists("NSE_FO.csv"):
                logger.warning("NSE_FO.csv not found. Skipping options scanning.")
                return True
            
            # Initialize options scanner (existing IndexScanner)
            options_scanner = IndexScanner(self.config)
            
            # Run options scan
            filtered_options_symbols = options_scanner.scan_symbols()
            
            if not filtered_options_symbols:
                logger.warning("No options symbols found matching the criteria")
                self.results['options']['symbols'] = []
                self.results['options']['summary'] = {'total_symbols': 0}
                return True
            
            # Generate summary statistics
            summary_stats = options_scanner.get_scan_summary(filtered_options_symbols)
            
            # Generate reports
            options_report_generator = ReportGenerator(
                output_dir=self.config.output_dir, 
                config=self.config
            )
            
            report_files = options_report_generator.generate_full_report(
                filtered_options_symbols, 
                summary_stats
            )
            
            # Store results
            self.results['options']['symbols'] = filtered_options_symbols
            self.results['options']['summary'] = summary_stats
            self.results['options']['reports'] = report_files
            
            logger.info("=" * 60)
            logger.info("OPTIONS SCANNING COMPLETED")
            logger.info("=" * 60)
            logger.info(f"Options symbols found: {len(filtered_options_symbols)}")
            logger.info(f"Options CSV Report: {report_files['csv_report']}")
            logger.info(f"Options Summary Report: {report_files['summary_report']}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error during options scanning: {e}", exc_info=True)
            return False
    
    def generate_combined_summary(self) -> str:
        """
        Generate a combined summary of both equity and options scanning results.
        
        Returns:
            Path to the combined summary file
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"combined_scan_summary_{timestamp}.txt"
            filepath = os.path.join(self.config.output_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                # Header
                f.write("=" * 80 + "\n")
                f.write("UNIFIED SCANNER COMBINED SUMMARY REPORT\n")
                f.write("=" * 80 + "\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                # Overall Statistics
                equity_count = self.results['equity']['summary'].get('total_symbols', 0)
                options_count = self.results['options']['summary'].get('total_symbols', 0)
                total_count = equity_count + options_count
                
                f.write("OVERALL STATISTICS:\n")
                f.write("-" * 40 + "\n")
                f.write(f"Total Symbols Found: {total_count}\n")
                f.write(f"  - Equity Symbols: {equity_count}\n")
                f.write(f"  - Options Symbols: {options_count}\n\n")
                
                # Equity Results
                f.write("EQUITY SCANNING RESULTS (NSE_CM.csv):\n")
                f.write("-" * 40 + "\n")
                if equity_count > 0:
                    equity_summary = self.results['equity']['summary']
                    f.write(f"Symbols Found: {equity_count}\n")
                    f.write(f"Unique Underlyings: {equity_summary.get('unique_underlyings', 0)}\n")
                    f.write(f"Average Volume: {equity_summary.get('avg_volume', 0):,}\n")
                    f.write(f"Average LTP: ₹{equity_summary.get('avg_ltp', 0.0):.2f}\n")
                    
                    if 'reports' in self.results['equity']:
                        reports = self.results['equity']['reports']
                        f.write(f"CSV Report: {os.path.basename(reports.get('csv_report', 'N/A'))}\n")
                        f.write(f"Summary Report: {os.path.basename(reports.get('summary_report', 'N/A'))}\n")
                else:
                    f.write("No equity symbols found matching criteria\n")
                f.write("\n")
                
                # Options Results
                f.write("OPTIONS SCANNING RESULTS (NSE_FO.csv):\n")
                f.write("-" * 40 + "\n")
                if options_count > 0:
                    options_summary = self.results['options']['summary']
                    f.write(f"Symbols Found: {options_count}\n")
                    f.write(f"Unique Underlyings: {options_summary.get('unique_underlyings', 0)}\n")
                    f.write(f"Average Volume: {options_summary.get('avg_volume', 0):,}\n")
                    f.write(f"Average LTP: ₹{options_summary.get('avg_ltp', 0.0):.2f}\n")
                    
                    if 'reports' in self.results['options']:
                        reports = self.results['options']['reports']
                        f.write(f"CSV Report: {os.path.basename(reports.get('csv_report', 'N/A'))}\n")
                        f.write(f"Summary Report: {os.path.basename(reports.get('summary_report', 'N/A'))}\n")
                else:
                    f.write("No options symbols found matching criteria\n")
                f.write("\n")
                
                # Configuration Used
                f.write("CONFIGURATION USED:\n")
                f.write("-" * 40 + "\n")
                f.write(f"Target Symbols: {', '.join(self.config.symbols)}\n")
                f.write(f"Volume Filter: {self.config.min_volume:,} - {self.config.max_volume:,}\n")
                f.write(f"LTP Filter: ₹{self.config.min_ltp_price:.2f} - ₹{self.config.max_ltp_price:.2f}\n")
                
                if self.config.mae_enabled:
                    f.write(f"MAE Indicator: Enabled (Length: {self.config.mae_length})\n")
                else:
                    f.write("MAE Indicator: Disabled\n")
                f.write("\n")
                
                f.write("=" * 80 + "\n")
                f.write("End of Combined Report\n")
                f.write("=" * 80 + "\n")
            
            logger.info(f"Combined summary report generated: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error generating combined summary: {e}")
            raise
    
    def run_unified_scan(self) -> bool:
        """
        Run the complete unified scanning process.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("=" * 80)
            logger.info("UNIFIED SCANNER APPLICATION STARTED")
            logger.info("=" * 80)
            logger.info(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Step 1: Validate prerequisites
            if not self.validate_prerequisites():
                return False
            
            # Step 2: Download symbol files
            if not self.download_symbol_files():
                return False
            
            # Step 3: Scan equity symbols (NSE_CM.csv) - First flow
            if not self.scan_equity_symbols():
                logger.error("Equity scanning failed")
                return False
            
            # Step 4: Scan options symbols (NSE_FO.csv) - Second flow
            if not self.scan_options_symbols():
                logger.error("Options scanning failed")
                return False
            
            # Step 5: Generate combined summary
            combined_summary_path = self.generate_combined_summary()
            
            # Step 6: Log completion
            logger.info("=" * 80)
            logger.info("UNIFIED SCANNER COMPLETED SUCCESSFULLY")
            logger.info("=" * 80)
            logger.info(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            equity_count = len(self.results['equity']['symbols'])
            options_count = len(self.results['options']['symbols'])
            total_count = equity_count + options_count
            
            logger.info(f"Total symbols found: {total_count}")
            logger.info(f"  - Equity symbols: {equity_count}")
            logger.info(f"  - Options symbols: {options_count}")
            logger.info(f"Combined summary: {combined_summary_path}")
            
            # Print final message to console
            print(f"\nUnified scanning completed successfully!")
            print(f"Total symbols found: {total_count}")
            print(f"  - Equity symbols: {equity_count}")
            print(f"  - Options symbols: {options_count}")
            print(f"Reports saved to: {self.config.output_dir}")
            
            if equity_count > 0:
                equity_reports = self.results['equity']['reports']
                print(f"   - Equity CSV: {os.path.basename(equity_reports.get('csv_report', 'N/A'))}")
                print(f"   - Equity Summary: {os.path.basename(equity_reports.get('summary_report', 'N/A'))}")
            
            if options_count > 0:
                options_reports = self.results['options']['reports']
                print(f"   - Options CSV: {os.path.basename(options_reports.get('csv_report', 'N/A'))}")
                print(f"   - Options Summary: {os.path.basename(options_reports.get('summary_report', 'N/A'))}")
            
            print(f"   - Combined Summary: {os.path.basename(combined_summary_path)}")
            
            return True
            
        except KeyboardInterrupt:
            logger.info("Application interrupted by user")
            print("\nApplication interrupted by user")
            return False
        except Exception as e:
            logger.error(f"Unified scanner failed with error: {e}", exc_info=True)
            print(f"\nUnified scanner failed: {e}")
            print("Check the log files for detailed error information")
            return False

def main():
    """Main function for unified scanner."""
    # Setup logging
    logger = setup_logging(level=logging.INFO, log_to_file=True)
    
    # Change working directory to the script's directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # Initialize and run unified scanner
    scanner = UnifiedScanner()
    success = scanner.run_unified_scan()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()